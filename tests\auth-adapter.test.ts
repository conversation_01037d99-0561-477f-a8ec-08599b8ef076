/**
 * Test suite for the Supabase Better Auth adapter
 * Ensures that account creation works correctly without database constraint violations
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { supabaseAdapter } from '@/lib/supabase-adapter';
import { supabaseAdmin } from '@/lib/supabase';

describe('Supabase Better Auth Adapter', () => {
  const testUserId = 'test-user-' + Date.now();
  const testAccountId = 'test-account-' + Date.now();

  afterAll(async () => {
    // Clean up test data
    await supabaseAdmin.from('accounts').delete().eq('user_id', testUserId);
    await supabaseAdmin.from('users').delete().eq('id', testUserId);
  });

  it('should create account with proper ID handling', async () => {
    // First create a test user
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .insert({
        id: testUserId,
        email: `test-${Date.now()}@example.com`,
        name: 'Test User',
        email_verified: false,
      })
      .select()
      .single();

    expect(userError).toBeNull();
    expect(user).toBeDefined();

    // Create adapter instance
    const adapter = supabaseAdapter({
      debugLogs: true,
    });

    // Get the adapter functions
    const adapterInstance = adapter({
      generateId: () => testAccountId,
    });

    // Test account creation
    const accountData = {
      id: testAccountId,
      accountId: 'credential-account',
      providerId: 'credential',
      userId: testUserId,
      password: 'hashed-password',
    };

    const createdAccount = await adapterInstance.adapter({}).create({
      model: 'account',
      data: accountData,
    });

    expect(createdAccount).toBeDefined();
    expect(createdAccount.id).toBe(testAccountId);
    expect(createdAccount.accountId).toBe('credential-account');
    expect(createdAccount.providerId).toBe('credential');
    expect(createdAccount.userId).toBe(testUserId);

    // Verify the account was actually created in the database
    const { data: dbAccount, error: dbError } = await supabaseAdmin
      .from('accounts')
      .select('*')
      .eq('id', testAccountId)
      .single();

    expect(dbError).toBeNull();
    expect(dbAccount).toBeDefined();
    expect(dbAccount.id).toBe(testAccountId);
  });

  it('should handle UUID tables correctly', async () => {
    const adapter = supabaseAdapter({
      debugLogs: true,
    });

    const adapterInstance = adapter({
      generateId: () => crypto.randomUUID(),
    });

    // Test user creation (UUID table)
    const userData = {
      email: `test-uuid-${Date.now()}@example.com`,
      name: 'Test UUID User',
      emailVerified: false,
    };

    const createdUser = await adapterInstance.adapter({}).create({
      model: 'user',
      data: userData,
    });

    expect(createdUser).toBeDefined();
    expect(createdUser.id).toBeDefined();
    expect(typeof createdUser.id).toBe('string');
    expect(createdUser.email).toBe(userData.email);

    // Clean up
    await supabaseAdmin.from('users').delete().eq('id', createdUser.id);
  });
});
