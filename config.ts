/**
 * Application Configuration
 * 
 * Central configuration file for the SaaS boilerplate application.
 * Contains settings for Creem integration, app metadata, and other configurations.
 */

import { ConfigProps } from "@/types/config";

const config = {
  // App metadata
  appName: "SaaS Boilerplate",
  appDescription: "A modern SaaS boilerplate with Creem payments and Supabase",
  domainName: "localhost:3000",

  // Creem configuration
  creem: {
    url: process.env.NODE_ENV === 'production' 
      ? "https://api.creem.io" 
      : "https://api-test.creem.io",
    pricingTableId: process.env.CREEM_PRICING_TABLE_ID || "default-pricing-table",
    apiKey: process.env.CREEM_API_KEY,
  },

  // Email configuration (placeholder for future use)
  email: {
    subdomain: "mail",
    fromNoReply: "noreply@localhost",
    fromAdmin: "admin@localhost",
    supportEmail: "support@localhost",
    forwardRepliesTo: "admin@localhost",
  },

  // Stripe configuration (placeholder - not used with Creem)
  stripe: {
    plans: [],
  },

  // App URLs
  urls: {
    base: process.env.BETTER_AUTH_URL || "http://localhost:3000",
    success: process.env.SUCCESS_URL || "http://localhost:3000/account",
  },
} as const;

export default config;
