/**
 * Supabase Database Types
 * 
 * Auto-generated TypeScript types for the Supabase database schema.
 * These types ensure type safety across all database operations.
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type SubscriptionStatus = 'active' | 'canceled' | 'expired' | 'past_due'

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string | null
          email_verified: boolean
          image: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name?: string | null
          email_verified?: boolean
          image?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string | null
          email_verified?: boolean
          image?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      sessions: {
        Row: {
          id: string
          expires_at: string
          token: string
          created_at: string
          updated_at: string
          ip_address: string | null
          user_agent: string | null
          user_id: string
        }
        Insert: {
          id: string
          expires_at: string
          token: string
          created_at?: string
          updated_at?: string
          ip_address?: string | null
          user_agent?: string | null
          user_id: string
        }
        Update: {
          id?: string
          expires_at?: string
          token?: string
          created_at?: string
          updated_at?: string
          ip_address?: string | null
          user_agent?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      accounts: {
        Row: {
          id: string
          account_id: string
          provider_id: string
          user_id: string
          access_token: string | null
          refresh_token: string | null
          id_token: string | null
          access_token_expires_at: string | null
          refresh_token_expires_at: string | null
          scope: string | null
          password: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          account_id: string
          provider_id: string
          user_id: string
          access_token?: string | null
          refresh_token?: string | null
          id_token?: string | null
          access_token_expires_at?: string | null
          refresh_token_expires_at?: string | null
          scope?: string | null
          password?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          account_id?: string
          provider_id?: string
          user_id?: string
          access_token?: string | null
          refresh_token?: string | null
          id_token?: string | null
          access_token_expires_at?: string | null
          refresh_token_expires_at?: string | null
          scope?: string | null
          password?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "accounts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      subscriptions: {
        Row: {
          id: string
          user_id: string
          product: string
          provider_customer_id: string
          status: SubscriptionStatus
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          user_id: string
          product: string
          provider_customer_id: string
          status?: SubscriptionStatus
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product?: string
          provider_customer_id?: string
          status?: SubscriptionStatus
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      one_time_purchases: {
        Row: {
          id: string
          user_id: string
          product: string
          provider_customer_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          user_id: string
          product: string
          provider_customer_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product?: string
          provider_customer_id?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "one_time_purchases_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      verifications: {
        Row: {
          id: string
          identifier: string
          value: string
          expires_at: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id: string
          identifier: string
          value: string
          expires_at: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          identifier?: string
          value?: string
          expires_at?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      subscription_status: SubscriptionStatus
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Convenience types for common operations
export type User = Database['public']['Tables']['users']['Row']
export type UserInsert = Database['public']['Tables']['users']['Insert']
export type UserUpdate = Database['public']['Tables']['users']['Update']

export type Session = Database['public']['Tables']['sessions']['Row']
export type SessionInsert = Database['public']['Tables']['sessions']['Insert']
export type SessionUpdate = Database['public']['Tables']['sessions']['Update']

export type Account = Database['public']['Tables']['accounts']['Row']
export type AccountInsert = Database['public']['Tables']['accounts']['Insert']
export type AccountUpdate = Database['public']['Tables']['accounts']['Update']

export type Subscription = Database['public']['Tables']['subscriptions']['Row']
export type SubscriptionInsert = Database['public']['Tables']['subscriptions']['Insert']
export type SubscriptionUpdate = Database['public']['Tables']['subscriptions']['Update']

export type OneTimePurchase = Database['public']['Tables']['one_time_purchases']['Row']
export type OneTimePurchaseInsert = Database['public']['Tables']['one_time_purchases']['Insert']
export type OneTimePurchaseUpdate = Database['public']['Tables']['one_time_purchases']['Update']

export type Verification = Database['public']['Tables']['verifications']['Row']
export type VerificationInsert = Database['public']['Tables']['verifications']['Insert']
export type VerificationUpdate = Database['public']['Tables']['verifications']['Update']
