"use client";

import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { 
  CreditCard, 
  Shield, 
  Zap, 
  BarChart3, 
  Globe, 
  Smartphone,
  Lock,
  Webhook,
  ArrowRight,
  CheckCircle,
  TrendingUp,
  Users
} from "lucide-react";
import Balancer from "react-wrap-balancer";

const fadeInUpVariant = {
  hidden: { y: 40, opacity: 0 },
  visible: (delay = 0) => ({
    y: 0,
    opacity: 1,
    transition: {
      ease: "easeOut",
      duration: 0.6,
      delay,
    },
  }),
};

const scaleInVariant = {
  hidden: { scale: 0.95, opacity: 0 },
  visible: (delay = 0) => ({
    scale: 1,
    opacity: 1,
    transition: {
      ease: "easeOut",
      duration: 0.5,
      delay,
    },
  }),
};

interface BentoCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  className?: string;
  featured?: boolean;
  badge?: string;
  stats?: { label: string; value: string }[];
  children?: React.ReactNode;
}

const BentoCard = ({ 
  title, 
  description, 
  icon: Icon, 
  className = "", 
  featured = false,
  badge,
  stats,
  children 
}: BentoCardProps) => (
  <Card className={`
    p-6 md:p-8 h-full flex flex-col transition-all duration-300 group hover:scale-[1.02] cursor-pointer
    ${featured 
      ? "border-orange-500/30 bg-gradient-to-br from-orange-950/20 via-black/40 to-black/60 backdrop-blur shadow-2xl shadow-orange-500/10" 
      : "border-neutral-800/50 bg-black/40 backdrop-blur hover:border-neutral-700/50"
    }
    ${className}
  `}>
    <div className="flex items-start justify-between mb-4">
      <div className={`p-3 rounded-xl ${featured ? "bg-orange-500/20" : "bg-neutral-800/50"} backdrop-blur-sm`}>
        <Icon className={`w-6 h-6 ${featured ? "text-orange-400" : "text-neutral-300"}`} />
      </div>
      {badge && (
        <Badge variant="secondary" className="bg-neutral-800/50 text-neutral-300 backdrop-blur-sm border-neutral-700/50">
          {badge}
        </Badge>
      )}
    </div>
    
    <div className="flex-1">
      <h3 className="text-xl font-bold text-white mb-3 font-mono group-hover:text-orange-300 transition-colors">
        {title}
      </h3>
      <p className="text-neutral-400 text-sm leading-relaxed mb-4">
        {description}
      </p>
      
      {stats && (
        <div className="grid grid-cols-2 gap-4 mb-4">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-2xl font-bold text-white font-mono">{stat.value}</div>
              <div className="text-xs text-neutral-500">{stat.label}</div>
            </div>
          ))}
        </div>
      )}
      
      {children}
    </div>
    
    <div className="flex items-center text-sm text-neutral-400 group-hover:text-orange-300 transition-colors mt-4">
      <span>Learn more</span>
      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
    </div>
  </Card>
);

export function BentoGrid() {
  const features = [
    {
      title: "Secure Payments",
      description: "Enterprise-grade security with end-to-end encryption. PCI DSS compliant infrastructure ensures your transactions are always protected.",
      icon: Shield,
      featured: true,
      badge: "Most Popular",
      stats: [
        { label: "Uptime", value: "99.9%" },
        { label: "Security", value: "PCI DSS" }
      ]
    },
    {
      title: "Real-time Analytics",
      description: "Comprehensive dashboard with real-time insights into your payment performance and customer behavior.",
      icon: BarChart3,
      stats: [
        { label: "Data Points", value: "50+" },
        { label: "Updates", value: "Live" }
      ]
    },
    {
      title: "Global Reach",
      description: "Accept payments from customers worldwide with support for 100+ currencies and local payment methods.",
      icon: Globe,
      badge: "Global"
    },
    {
      title: "Lightning Fast",
      description: "Sub-second payment processing with optimized infrastructure for maximum performance and reliability.",
      icon: Zap,
      stats: [
        { label: "Processing", value: "<1s" },
        { label: "Success Rate", value: "99.8%" }
      ]
    },
    {
      title: "Mobile Optimized",
      description: "Seamless mobile experience with responsive design and native mobile payment support including Apple Pay and Google Pay.",
      icon: Smartphone
    },
    {
      title: "Webhook Integration",
      description: "Real-time notifications and automated workflows with reliable webhook delivery and retry mechanisms.",
      icon: Webhook,
      badge: "Developer"
    }
  ];

  return (
    <section className="relative py-24 md:py-32">

      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={fadeInUpVariant}
        >
          <Badge variant="secondary" className="rounded-full px-4 py-1 bg-white/10 dark:bg-neutral-800/50 text-neutral-200 backdrop-blur-sm border-0 shadow-sm mb-6">
            <CreditCard className="w-4 h-4 mr-2" />
            <span className="text-sm">Powerful Features</span>
          </Badge>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
            <Balancer>
              Everything You Need to
              <span className="bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent"> Scale</span>
            </Balancer>
          </h2>
          
          <p className="text-lg text-neutral-400 max-w-2xl mx-auto">
            <Balancer>
              Built for modern businesses, our platform provides all the tools you need to accept payments, 
              manage subscriptions, and grow your revenue.
            </Balancer>
          </p>
        </motion.div>

        {/* Bento Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {/* Large featured card - spans 2 columns on lg+ */}
          <motion.div
            className="lg:col-span-2"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={scaleInVariant}
            custom={0}
          >
            <BentoCard {...features[0]} className="h-full">
              <div className="flex items-center gap-4 mt-4">
                <div className="flex items-center gap-2 text-green-400">
                  <CheckCircle className="w-4 h-4" />
                  <span className="text-sm">SOC 2 Certified</span>
                </div>
                <div className="flex items-center gap-2 text-green-400">
                  <Lock className="w-4 h-4" />
                  <span className="text-sm">256-bit Encryption</span>
                </div>
              </div>
            </BentoCard>
          </motion.div>

          {/* Analytics card */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={scaleInVariant}
            custom={0.1}
          >
            <BentoCard {...features[1]}>
              <div className="mt-4 space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-neutral-400">Revenue Growth</span>
                  <div className="flex items-center gap-1 text-green-400">
                    <TrendingUp className="w-3 h-3" />
                    <span>+24%</span>
                  </div>
                </div>
                <div className="w-full bg-neutral-800 rounded-full h-2">
                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 h-2 rounded-full w-3/4"></div>
                </div>
              </div>
            </BentoCard>
          </motion.div>

          {/* Global reach card */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={scaleInVariant}
            custom={0.2}
          >
            <BentoCard {...features[2]}>
              <div className="mt-4 grid grid-cols-3 gap-2 text-xs">
                {["USD", "EUR", "GBP", "JPY", "CAD", "AUD"].map((currency) => (
                  <div key={currency} className="bg-neutral-800/50 rounded px-2 py-1 text-center text-neutral-300">
                    {currency}
                  </div>
                ))}
              </div>
            </BentoCard>
          </motion.div>

          {/* Lightning fast card */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={scaleInVariant}
            custom={0.3}
          >
            <BentoCard {...features[3]}>
              <div className="mt-4">
                <div className="flex items-center gap-2 text-orange-400 text-sm">
                  <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                  <span>Processing payment...</span>
                </div>
              </div>
            </BentoCard>
          </motion.div>

          {/* Mobile optimized card - spans 2 columns on lg+ */}
          <motion.div
            className="lg:col-span-2"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={scaleInVariant}
            custom={0.4}
          >
            <BentoCard {...features[4]} className="h-full">
              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2 text-blue-400">
                    <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                    <span className="text-sm">Apple Pay</span>
                  </div>
                  <div className="flex items-center gap-2 text-green-400">
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    <span className="text-sm">Google Pay</span>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-neutral-400">
                  <Users className="w-4 h-4" />
                  <span className="text-sm">Mobile-first</span>
                </div>
              </div>
            </BentoCard>
          </motion.div>

          {/* Webhook card */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={scaleInVariant}
            custom={0.5}
          >
            <BentoCard {...features[5]}>
              <div className="mt-4 space-y-2">
                <div className="text-xs font-mono bg-neutral-900/50 rounded p-2 border border-neutral-800/50">
                  <div className="text-green-400">POST /webhook</div>
                  <div className="text-neutral-500">{"{ status: 'success' }"}</div>
                </div>
              </div>
            </BentoCard>
          </motion.div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={fadeInUpVariant}
          custom={0.6}
        >
          <Button
            size="lg"
            className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white border-0 px-8 py-3"
          >
            Explore All Features
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
