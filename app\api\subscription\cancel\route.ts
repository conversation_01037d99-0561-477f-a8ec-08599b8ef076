/**
 * Secure Subscription Cancellation API Route
 *
 * Handles subscription cancellation requests with comprehensive security validation.
 * Integrates with both Creem SDK and Supabase database for consistency.
 *
 * Features:
 * - User authentication validation
 * - Subscription ownership verification
 * - Database synchronization
 * - Comprehensive error handling
 *
 * @module api/subscription/cancel
 */

import { NextRequest, NextResponse } from "next/server";
import { Creem } from "creem";
import { headers } from "next/headers";
import { auth } from "@/lib/auth";
import { subscriptionOperations } from "@/lib/database";
import { DatabaseError } from "@/lib/supabase";
import { SubscriptionStatus } from "@/types/database";

/**
 * Initialize Creem SDK client
 * Server index 1 is used for test environment
 */
const creem = new Creem({
  serverIdx: 1,
});

/**
 * POST /api/subscription/cancel
 *
 * Securely cancels an active subscription for the authenticated user.
 * Includes ownership validation and database synchronization.
 * The subscription will remain active until the end of the current billing period.
 *
 * Security Features:
 * - Verifies user authentication
 * - Validates subscription ownership (prevents unauthorized cancellations)
 * - Checks subscription status before cancellation
 * - Updates local database for consistency
 *
 * @async
 * @function
 * @param {NextRequest} req - Next.js request object containing:
 *   - subscription_id: Query parameter for the subscription to cancel
 *
 * @returns {Promise<NextResponse>}
 * - Success: JSON response with success message and 200 status
 * - Error: JSON response with error message and appropriate status code
 *
 * @example
 * // Request
 * POST /api/subscription/cancel?subscription_id=sub_123
 *
 * // Success Response
 * {
 *   "message": "Subscription canceled successfully"
 * }
 * Status: 200 OK
 *
 * // Error Responses
 * {
 *   "error": "Unauthorized"
 * }
 * Status: 401 Unauthorized
 *
 * {
 *   "error": "Subscription not found or access denied"
 * }
 * Status: 404 Not Found
 *
 * {
 *   "error": "Subscription is already canceled or expired"
 * }
 * Status: 400 Bad Request
 */
export async function POST(req: NextRequest) {
  try {
    // Get authenticated session and subscription ID
    const session = await auth.api.getSession({ headers: headers() });
    const subscriptionId = req.nextUrl.searchParams.get("subscription_id");
    const apiKey = process.env.CREEM_API_KEY;

    // Verify authentication and subscription ID
    if (!subscriptionId || !session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // SECURITY: Verify that the subscription belongs to the authenticated user
    // This prevents users from canceling other users' subscriptions
    const userSubscriptions = await subscriptionOperations.findByUserId(session.user.id);
    const subscriptionToCancel = userSubscriptions.find(sub => sub.id === subscriptionId);

    if (!subscriptionToCancel) {
      return NextResponse.json(
        { error: "Subscription not found or access denied" },
        { status: 404 }
      );
    }

    // Check if subscription is already canceled or expired
    if (subscriptionToCancel.status === "canceled" || subscriptionToCancel.status === "expired") {
      return NextResponse.json(
        { error: "Subscription is already canceled or expired" },
        { status: 400 }
      );
    }

    // Call Creem SDK to cancel the subscription
    // This will prevent renewal at the end of the current period
    await creem.cancelSubscription({
      xApiKey: apiKey as string,
      id: subscriptionId as string,
    });

    // Update local database to reflect the cancellation
    // This ensures consistency between Creem and our local database
    await subscriptionOperations.update(subscriptionId, {
      status: "canceled" as SubscriptionStatus,
      updated_at: new Date().toISOString(),
    });

    // Return success response
    return NextResponse.json(
      { message: "Subscription canceled successfully" },
      { status: 200 }
    );

  } catch (error) {
    console.error("Error canceling subscription:", error);

    // Handle database errors specifically
    if (error instanceof DatabaseError) {
      return NextResponse.json(
        { error: "Database error occurred while canceling subscription" },
        { status: 500 }
      );
    }

    // Handle general errors
    return NextResponse.json(
      { error: "Failed to cancel subscription" },
      { status: 500 }
    );
  }
}
