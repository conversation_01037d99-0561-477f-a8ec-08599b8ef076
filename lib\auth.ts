import { betterAuth } from "better-auth";
import { supabaseAdapter } from "./supabase-adapter";

/**
 * Better Auth configuration using pure Supabase adapter
 * This uses a custom Supabase adapter that integrates directly with Supabase client
 * instead of using DATABASE_URL or PostgreSQL connection strings
 */
export const auth = betterAuth({
  database: supabaseAdapter({
    debugLogs: process.env.NODE_ENV === 'development',
  }),
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID as string,
      clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  advanced: {
    crossSubDomainCookies: {
      enabled: false,
    },
  },
});
