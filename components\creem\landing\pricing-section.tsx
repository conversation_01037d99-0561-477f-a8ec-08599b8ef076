"use client";

import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Zap, Star, ArrowRight } from "lucide-react";
import { authClient } from "@/lib/auth-client";
import Balancer from "react-wrap-balancer";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  imageUrl?: string;
  status: string;
  billingType: string;
  billingPeriod: string;
  mode: string;
  object: string;
}

const fadeInUpVariant = {
  hidden: { y: 40, opacity: 0 },
  visible: (delay = 0) => ({
    y: 0,
    opacity: 1,
    transition: {
      ease: "easeOut",
      duration: 0.6,
      delay,
    },
  }),
};

const scaleInVariant = {
  hidden: { scale: 0.95, opacity: 0 },
  visible: (delay = 0) => ({
    scale: 1,
    opacity: 1,
    transition: {
      ease: "easeOut",
      duration: 0.5,
      delay,
    },
  }),
};

export function PricingSection() {
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isProcessing, setIsProcessing] = useState<string | null>(null);

  useEffect(() => {
    // Check authentication status
    authClient.getSession().then(({ data }) => {
      setIsLoggedIn(!!data?.user);
    });

    // Fetch products
    async function fetchProducts() {
      try {
        const response = await fetch("/api/products");
        if (!response.ok) {
          throw new Error("Failed to fetch products");
        }
        const data = await response.json();

        if (!data.items || !Array.isArray(data.items)) {
          throw new Error("Invalid data format from API");
        }
        setProducts(data.items);
      } catch (err) {
        console.error("Fetch error:", err);
        setError(
          err instanceof Error ? err.message : "Failed to fetch products",
        );
      } finally {
        setIsLoading(false);
      }
    }

    fetchProducts();
  }, []);

  const handleGetStarted = async (productId: string) => {
    if (!isLoggedIn) {
      router.push("/signup");
      return;
    }

    try {
      setIsProcessing(productId);
      const response = await fetch(`/api/checkout?product_id=${productId}`);
      if (!response.ok) {
        throw new Error("Failed to create checkout session");
      }
      const data = await response.json();
      
      if (!data.checkoutUrl) {
        throw new Error("Invalid checkout URL received");
      }

      window.location.href = data.checkoutUrl;
    } catch (err) {
      console.error("Checkout error:", err);
      setError(err instanceof Error ? err.message : "Failed to process checkout");
    } finally {
      setIsProcessing(null);
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price / 100);
  };

  const formatBillingPeriod = (billingPeriod: string) => {
    return billingPeriod.replace("every-", "").replace("ly", "");
  };

  const getPopularProduct = () => {
    // Mark the middle product as popular, or the second one if there are only 2
    if (products.length === 0) return null;
    const middleIndex = Math.floor(products.length / 2);
    return products[middleIndex]?.id;
  };

  if (error) {
    return (
      <section className="relative py-24 md:py-32">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card className="p-8 border border-red-800/50 bg-black/40 backdrop-blur text-center">
            <p className="text-red-400 mb-4">Error loading pricing: {error}</p>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="border-neutral-800 text-neutral-200 hover:bg-neutral-800/20"
            >
              Try again
            </Button>
          </Card>
        </div>
      </section>
    );
  }

  return (
    <section id="pricing" className="relative py-24 md:py-32">
      
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={fadeInUpVariant}
        >
          <Badge variant="secondary" className="rounded-full px-4 py-1 bg-white/10 dark:bg-neutral-800/50 text-neutral-200 backdrop-blur-sm border-0 shadow-sm mb-6">
            <Zap className="w-4 h-4 mr-2" />
            <span className="text-sm">Choose Your Plan</span>
          </Badge>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
            <Balancer>
              Simple, Transparent Pricing
            </Balancer>
          </h2>
          
          <p className="text-lg text-neutral-400 max-w-2xl mx-auto">
            <Balancer>
              Get started with our flexible pricing plans. All plans include our core features with no hidden fees.
            </Balancer>
          </p>
        </motion.div>

        {/* Pricing Cards */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[...Array(3)].map((_, i) => (
              <Card
                key={i}
                className="p-8 border border-neutral-800/50 bg-black/40 backdrop-blur animate-pulse"
              >
                <div className="space-y-6">
                  <div className="h-6 bg-neutral-800 rounded w-3/4"></div>
                  <div className="h-8 bg-neutral-800 rounded w-1/2"></div>
                  <div className="h-4 bg-neutral-800 rounded w-full"></div>
                  <div className="space-y-3">
                    {[...Array(4)].map((_, j) => (
                      <div key={j} className="h-4 bg-neutral-800 rounded w-5/6"></div>
                    ))}
                  </div>
                  <div className="h-10 bg-neutral-800 rounded"></div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {products.map((product, index) => {
              const isPopular = product.id === getPopularProduct();
              const delay = index * 0.1;
              
              return (
                <motion.div
                  key={product.id}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  variants={scaleInVariant}
                  custom={delay}
                  className="relative group"
                >
                  {isPopular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                      <Badge className="bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0 px-4 py-1">
                        <Star className="w-3 h-3 mr-1" />
                        Most Popular
                      </Badge>
                    </div>
                  )}
                  
                  <Card className={`p-8 h-full flex flex-col transition-all duration-300 group-hover:scale-105 ${
                    isPopular 
                      ? "border-blue-500/50 bg-gradient-to-b from-blue-950/20 to-black/40 backdrop-blur shadow-2xl shadow-blue-500/10" 
                      : "border-neutral-800/50 bg-black/40 backdrop-blur hover:border-neutral-700/50"
                  }`}>
                    <div className="flex-1">
                      <div className="mb-6">
                        <h3 className="text-xl font-bold text-white mb-2 font-mono">
                          {product.name}
                        </h3>
                        <p className="text-neutral-400 text-sm line-clamp-2">
                          {product.description.replace(/[#*`]/g, "")}
                        </p>
                      </div>

                      <div className="mb-6">
                        <div className="flex items-baseline">
                          <span className="text-4xl font-bold text-white font-mono">
                            {formatPrice(product.price, product.currency)}
                          </span>
                          {product.billingType === "recurring" && (
                            <span className="text-neutral-400 ml-2">
                              /{formatBillingPeriod(product.billingPeriod)}
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-neutral-500 mt-1">
                          {product.billingType === "recurring" 
                            ? `Billed ${formatBillingPeriod(product.billingPeriod)}ly` 
                            : "One-time payment"}
                        </p>
                      </div>

                      {/* Features placeholder - you can customize this based on your product data */}
                      <div className="space-y-3 mb-8">
                        {[
                          "Full access to all features",
                          "24/7 customer support",
                          "Regular updates included",
                          "30-day money-back guarantee"
                        ].map((feature, i) => (
                          <div key={i} className="flex items-center text-sm">
                            <Check className="w-4 h-4 text-green-400 mr-3 flex-shrink-0" />
                            <span className="text-neutral-300">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Button
                      onClick={() => handleGetStarted(product.id)}
                      disabled={isProcessing === product.id}
                      className={`w-full group/btn ${
                        isPopular
                          ? "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
                          : "bg-white text-black hover:bg-neutral-200"
                      }`}
                    >
                      {isProcessing === product.id ? (
                        "Processing..."
                      ) : (
                        <>
                          {isLoggedIn ? "Get Started" : "Sign Up"}
                          <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                        </>
                      )}
                    </Button>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        )}

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={fadeInUpVariant}
          custom={0.4}
        >
          <p className="text-neutral-400 mb-6">
            Need a custom solution? We&apos;d love to help you find the perfect plan.
          </p>
          <Button
            variant="outline"
            onClick={() => router.push("/contact")}
            className="border-neutral-700 text-neutral-200 hover:bg-neutral-800/20"
          >
            Contact Sales
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
