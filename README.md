# Creem Next.js + Supabase Template

A **Next.js App Router** template project for integrating [Creem](https://creem.io) with modern web apps using [Supabase](https://supabase.com) as the database. This template demonstrates how to use the [Creem SDK](https://github.com/armitage-labs/creem-sdk) to implement common subscription and payment flows with a secure, scalable PostgreSQL backend. Use this as a starting point for your own Creem-powered SaaS or product.

> **Note:** This is a template project. We welcome [issues](https://github.com/armitage-labs/creem-template/issues) for new use cases and [pull requests](https://github.com/armitage-labs/creem-template/pulls) to improve the template!

---

## 🚀 Quickstart

1. **Clone the repository**
2. **Install dependencies** (use your preferred package manager)

   ```bash
   yarn install
   # or
   npm install
   # or
   pnpm install
   ```

3. **Set up your environment variables**

   ```bash
   cp .env.example .env
   # Edit .env and fill in the required Supabase and Creem variables
   ```

4. **Set up your Supabase database**

   - Create a new project at [supabase.com](https://supabase.com)
   - Run the SQL schema from `supabase/schema.sql` in your Supabase SQL editor
   - Update your `.env` file with your Supabase project URL and keys

5. **Start the development server**

   ```bash
   yarn dev
   ```

6. **Expose your app to the internet for webhooks**

   To receive webhooks from Creem, you need a reverse proxy. We recommend [NGROK](https://ngrok.com/docs/getting-started/), which is free and easy to set up.

   - [NGROK Documentation](https://ngrok.com/docs/getting-started/)

---

## 📝 Introduction

This template provides a ready-to-use integration with Creem, Supabase, and Next.js App Router. It demonstrates:

- Fetching and displaying all products in your Creem account
- Creating checkout sessions for products
- Fulfilling orders based on the purchasing account
- Handling subscription creation, cancellation, and expiration
- Generating customer portal links for clients with active subscriptions

The codebase is modular, uses TypeScript throughout, and leverages modern React patterns with [Shadcn UI](https://ui.shadcn.com/), [Radix UI](https://www.radix-ui.com/), and [Tailwind CSS](https://tailwindcss.com/).
The template is not taking security into account, so please be careful when using any of its code in production.

---

## 🛠️ Technologies Used

- [Next.js](https://nextjs.org/) – React framework for server-side rendering and static site generation
- [Supabase](https://supabase.com/) – Open-source Firebase alternative with PostgreSQL database
- [Better Auth](https://www.better-auth.com/) – Comprehensive authentication framework for TypeScript
- [Creem SDK](https://www.npmjs.com/package/creem) – Subscription and payment integration
- [TypeScript](https://www.typescriptlang.org/) – Type-safe JavaScript development

---

## 🗄️ Database Setup

### Supabase Configuration

1. **Create a Supabase Project**
   - Go to [supabase.com](https://supabase.com) and create a new project
   - Wait for the project to be fully initialized

2. **Run the Database Schema**
   - Navigate to the SQL Editor in your Supabase dashboard
   - Copy and paste the contents of `supabase/schema.sql`
   - Execute the SQL to create all necessary tables and policies

3. **Get Your Credentials**
   - Go to Settings > API in your Supabase dashboard
   - Copy your Project URL, anon key, and service_role key
   - Update your `.env` file with these values

4. **Configure Row Level Security (RLS)**
   - The schema automatically enables RLS policies for secure data access
   - Users can only access their own data through the defined policies
   - The service role bypasses RLS for server-side operations

### Environment Variables

Make sure to set these in your `.env` file:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

---

## 💡 Contributing

- **Request new use cases:** [Create an issue](https://github.com/YOUR_REPO/issues)
- **Improve the template:** [Open a pull request](https://github.com/YOUR_REPO/pulls)

We welcome contributions and feedback from the community!

---

## 📚 Resources

- [Creem SDK Documentation](https://github.com/armitage-labs/creem-sdk)
- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Better Auth Documentation](https://www.better-auth.com/docs)
- [Shadcn UI](https://ui.shadcn.com/)
- [Radix UI](https://www.radix-ui.com/)
- [Tailwind CSS](https://tailwindcss.com/)

---

## License

MIT
