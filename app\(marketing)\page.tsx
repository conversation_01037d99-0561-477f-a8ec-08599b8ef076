"use client";

import { Container } from "@/components/creem/landing/container";
import { <PERSON> } from "@/components/creem/landing/hero";
import { BentoGrid } from "@/components/creem/landing/bento-grid";
import { PricingSection } from "@/components/creem/landing/pricing-section";
import { Footer } from "@/components/creem/landing/footer";

export default function Home() {
  return (
    <main className="relative min-h-screen">
      {/* Unified Background System for Entire Landing Page */}
      {/* Base Layer: Primary gradient background creating depth */}
      <div className="fixed inset-0 w-screen h-screen bg-gradient-to-b from-black via-neutral-950 to-neutral-900 -z-50" />

      {/* Texture Layer: Subtle noise overlay for visual richness */}
      <div className="fixed inset-0 w-screen h-screen opacity-[0.15] dark:opacity-[0.05] -z-40 [background-image:url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIiB4PSIwIiB5PSIwIj48ZmVUdXJidWxlbmNlIGJhc2VGcmVxdWVuY3k9Ii43NSIgc3RpdGNoVGlsZXM9InN0aXRjaCIgdHlwZT0iZnJhY3RhbE5vaXNlIi8+PGZlQ29sb3JNYXRyaXggdHlwZT0ic2F0dXJhdGUiIHZhbHVlcz0iMCIvPjwvZmlsdGVyPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbHRlcj0idXJsKCNhKSIgb3BhY2l0eT0iLjA1Ii8+PC9zdmc+')]" />

      {/* Pattern Layer: Dot matrix with radial fade for depth */}
      <div className="fixed inset-0 w-screen h-screen bg-[radial-gradient(#333333_1px,transparent_1px)] [background-size:16px_16px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_70%,transparent_100%)] opacity-40 dark:opacity-20 -z-30" />

      {/* Ambient Layer: Gradient orbs for dynamic lighting effects */}
      <div className="fixed inset-0 w-screen h-screen overflow-hidden -z-20">
        {/* Top-right ambient light source */}
        <div className="absolute -right-[40%] -top-[40%] w-[80%] h-[80%] rounded-full bg-gradient-to-br from-neutral-800/40 via-transparent to-transparent blur-3xl" />
        {/* Bottom-left ambient light source */}
        <div className="absolute -left-[40%] -bottom-[40%] w-[80%] h-[80%] rounded-full bg-gradient-to-tr from-neutral-800/40 via-transparent to-transparent blur-3xl" />
        {/* Center orange accent for Creem branding */}
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[60%] h-[60%] rounded-full bg-gradient-to-r from-orange-500/10 via-transparent to-orange-600/10 blur-3xl" />
      </div>

      {/* Grid Layer: Structural visual element */}
      <div className="fixed inset-0 w-screen h-screen bg-[linear-gradient(rgba(51,51,51,0.05)_1px,transparent_1px),linear-gradient(to_right,rgba(51,51,51,0.05)_1px,transparent_1px)] bg-[size:80px_80px] [mask-image:radial-gradient(ellipse_80%_80%_at_50%_50%,#000_40%,transparent_100%)] -z-10" />

      {/* Content sections - now without individual backgrounds */}
      <Container className="relative z-10">
        <Hero />
      </Container>
      <BentoGrid />
      <PricingSection />
      <Footer />
    </main>
  );
}