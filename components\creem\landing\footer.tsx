"use client";

import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { Github, Twitter, Linkedin, Mail, ExternalLink } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

const fadeInUpVariant = {
  hidden: { y: 40, opacity: 0 },
  visible: (delay = 0) => ({
    y: 0,
    opacity: 1,
    transition: {
      ease: "easeOut",
      duration: 0.6,
      delay,
    },
  }),
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

interface FooterLinkProps {
  href: string;
  children: React.ReactNode;
  external?: boolean;
}

const FooterLink = ({ href, children, external = false }: FooterLinkProps) => {
  const router = useRouter();
  
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (external) {
      window.open(href, '_blank', 'noopener,noreferrer');
    } else {
      router.push(href);
    }
  };

  return (
    <motion.a
      href={href}
      onClick={handleClick}
      className="text-neutral-400 hover:text-white transition-colors duration-200 text-sm flex items-center gap-1 group"
      whileHover={{ x: 2 }}
      transition={{ duration: 0.2 }}
    >
      {children}
      {external && (
        <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
      )}
    </motion.a>
  );
};

interface SocialLinkProps {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
}

const SocialLink = ({ href, icon: Icon, label }: SocialLinkProps) => (
  <motion.a
    href={href}
    target="_blank"
    rel="noopener noreferrer"
    className="p-2 rounded-lg bg-neutral-800/50 hover:bg-neutral-700/50 text-neutral-400 hover:text-white transition-all duration-200 backdrop-blur-sm border border-neutral-800/50 hover:border-neutral-700/50"
    whileHover={{ scale: 1.05, y: -2 }}
    whileTap={{ scale: 0.95 }}
    aria-label={label}
  >
    <Icon className="w-4 h-4" />
  </motion.a>
);

export function Footer() {
  const currentYear = new Date().getFullYear();

  const navigationLinks = [
    { name: "About", href: "/about" },
    { name: "Features", href: "/features" },
    { name: "Pricing", href: "/pricing" },
    { name: "Contact", href: "/contact" },
    { name: "Documentation", href: "/docs", external: true },
    { name: "API Reference", href: "/api-docs", external: true },
  ];

  const legalLinks = [
    { name: "Privacy Policy", href: "/privacy" },
    { name: "Terms of Service", href: "/terms" },
    { name: "Cookie Policy", href: "/cookies" },
    { name: "GDPR", href: "/gdpr" },
  ];

  const socialLinks = [
    { href: "https://github.com", icon: Github, label: "GitHub" },
    { href: "https://twitter.com", icon: Twitter, label: "Twitter" },
    { href: "https://linkedin.com", icon: Linkedin, label: "LinkedIn" },
    { href: "mailto:<EMAIL>", icon: Mail, label: "Email" },
  ];

  return (
    <footer className="relative overflow-hidden">
      {/* Large faded background text - similar to Interview Coder design */}
      <div className="absolute inset-0 flex items-end justify-center overflow-hidden -z-5">
        <div className="w-full text-center font-bold select-none pointer-events-none transform translate-y-1/4 font-mono"
             style={{
               fontSize: 'clamp(8rem, 20vw, 24rem)',
               lineHeight: '0.8',
               background: `linear-gradient(to right,
                 rgba(255, 190, 152, 0.15),
                 rgba(255, 190, 152, 0.25),
                 rgba(255, 190, 152, 0.20))`,
               WebkitBackgroundClip: 'text',
               backgroundClip: 'text',
               WebkitTextFillColor: 'transparent',
               color: 'transparent'
             }}>
          App Name
        </div>
      </div>

      {/* Top border with backdrop blur */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-neutral-800/50 to-transparent" />

      <div className="relative backdrop-blur-sm z-10">
        <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-20">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-12"
          >
            {/* Brand Section */}
            <motion.div variants={fadeInUpVariant} className="lg:col-span-1">
              <div className="mb-6">
                <h3 className="text-2xl font-bold text-white font-mono mb-2">
                  App Name
                </h3>
                <p className="text-neutral-400 text-sm leading-relaxed">
                  A modern SaaS platform built with Next.js, TypeScript, and Creem integration. 
                  Empowering businesses with seamless payment solutions.
                </p>
              </div>
              
              <Badge variant="secondary" className="rounded-full px-3 py-1 bg-neutral-800/50 text-neutral-300 backdrop-blur-sm border-neutral-700/50 mb-6">
                <span className="text-xs">Powered by Creem SDK</span>
              </Badge>

              {/* Social Links */}
              <div className="flex gap-3">
                {socialLinks.map((social, index) => (
                  <motion.div
                    key={social.label}
                    variants={fadeInUpVariant}
                    custom={index * 0.1}
                  >
                    <SocialLink {...social} />
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Navigation Links */}
            <motion.div variants={fadeInUpVariant} custom={0.1}>
              <h4 className="text-white font-semibold mb-4 font-mono">Navigation</h4>
              <ul className="space-y-3">
                {navigationLinks.map((link, index) => (
                  <motion.li
                    key={link.name}
                    variants={fadeInUpVariant}
                    custom={0.1 + index * 0.05}
                  >
                    <FooterLink href={link.href} external={link.external}>
                      {link.name}
                    </FooterLink>
                  </motion.li>
                ))}
              </ul>
            </motion.div>

            {/* Legal Links */}
            <motion.div variants={fadeInUpVariant} custom={0.2}>
              <h4 className="text-white font-semibold mb-4 font-mono">Legal</h4>
              <ul className="space-y-3">
                {legalLinks.map((link, index) => (
                  <motion.li
                    key={link.name}
                    variants={fadeInUpVariant}
                    custom={0.2 + index * 0.05}
                  >
                    <FooterLink href={link.href}>
                      {link.name}
                    </FooterLink>
                  </motion.li>
                ))}
              </ul>
            </motion.div>

            {/* Newsletter/Contact */}
            <motion.div variants={fadeInUpVariant} custom={0.3}>
              <h4 className="text-white font-semibold mb-4 font-mono">Stay Updated</h4>
              <p className="text-neutral-400 text-sm mb-4">
                Get the latest updates and news about our platform.
              </p>
              
              <div className="space-y-3">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full border-neutral-700 text-neutral-200 hover:bg-neutral-800/20 backdrop-blur-sm"
                  onClick={() => window.open('mailto:<EMAIL>')}
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Contact Us
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-neutral-400 hover:text-white hover:bg-neutral-800/20"
                  onClick={() => window.open('/docs', '_blank')}
                >
                  View Documentation
                </Button>
              </div>
            </motion.div>
          </motion.div>

          {/* Bottom Section */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUpVariant}
            custom={0.4}
            className="mt-12 pt-8 border-t border-neutral-800/50"
          >
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <p className="text-neutral-500 text-sm">
                © {currentYear} App Name. All rights reserved.
              </p>
              
              <div className="flex items-center gap-4 text-xs text-neutral-500">
                <span>Built with Next.js & Creem</span>
                <span className="hidden md:inline">•</span>
                <span>Made with ❤️ for developers</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </footer>
  );
}
