/**
 * Type-Safe Database Operations Layer
 * 
 * This module provides a high-level, type-safe interface for database operations
 * that replaces Prisma client functionality with Supabase operations.
 * All functions include proper error handling and type safety.
 */

import { supabase, supabaseAdmin, DatabaseError } from '@/lib/supabase';
import {
  User, UserInsert, UserUpdate,
  Session, SessionInsert, SessionUpdate,
  Account, AccountInsert, AccountUpdate,
  Subscription, SubscriptionInsert, SubscriptionUpdate,
  OneTimePurchase, OneTimePurchaseInsert, OneTimePurchaseUpdate,
  Verification, VerificationInsert, VerificationUpdate,
  SubscriptionStatus
} from '@/types/database';

/**
 * User Operations
 */
export const userOperations = {
  /**
   * Find user by ID
   */
  async findById(id: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw new DatabaseError(`Failed to find user: ${error.message}`, error.code);
    }

    return data;
  },

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new DatabaseError(`Failed to find user by email: ${error.message}`, error.code);
    }

    return data;
  },

  /**
   * Create new user
   */
  async create(userData: UserInsert): Promise<User> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .insert(userData)
      .select()
      .single();

    if (error) {
      throw new DatabaseError(`Failed to create user: ${error.message}`, error.code);
    }

    return data;
  },

  /**
   * Update user
   */
  async update(id: string, userData: UserUpdate): Promise<User> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .update(userData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new DatabaseError(`Failed to update user: ${error.message}`, error.code);
    }

    return data;
  }
};

/**
 * Session Operations
 */
export const sessionOperations = {
  /**
   * Create new session
   */
  async create(sessionData: SessionInsert): Promise<Session> {
    const { data, error } = await supabaseAdmin
      .from('sessions')
      .insert(sessionData)
      .select()
      .single();

    if (error) {
      throw new DatabaseError(`Failed to create session: ${error.message}`, error.code);
    }

    return data;
  },

  /**
   * Find session by token
   */
  async findByToken(token: string): Promise<Session | null> {
    const { data, error } = await supabaseAdmin
      .from('sessions')
      .select('*')
      .eq('token', token)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new DatabaseError(`Failed to find session: ${error.message}`, error.code);
    }

    return data;
  },

  /**
   * Delete session
   */
  async delete(id: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('sessions')
      .delete()
      .eq('id', id);

    if (error) {
      throw new DatabaseError(`Failed to delete session: ${error.message}`, error.code);
    }
  },

  /**
   * Delete sessions by user ID
   */
  async deleteByUserId(userId: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('sessions')
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw new DatabaseError(`Failed to delete user sessions: ${error.message}`, error.code);
    }
  }
};

/**
 * Account Operations
 */
export const accountOperations = {
  /**
   * Create new account
   */
  async create(accountData: AccountInsert): Promise<Account> {
    const { data, error } = await supabaseAdmin
      .from('accounts')
      .insert(accountData)
      .select()
      .single();

    if (error) {
      throw new DatabaseError(`Failed to create account: ${error.message}`, error.code);
    }

    return data;
  },

  /**
   * Find account by provider and account ID
   */
  async findByProvider(providerId: string, accountId: string): Promise<Account | null> {
    const { data, error } = await supabaseAdmin
      .from('accounts')
      .select('*')
      .eq('provider_id', providerId)
      .eq('account_id', accountId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new DatabaseError(`Failed to find account: ${error.message}`, error.code);
    }

    return data;
  },

  /**
   * Update account
   */
  async update(id: string, accountData: AccountUpdate): Promise<Account> {
    const { data, error } = await supabaseAdmin
      .from('accounts')
      .update(accountData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new DatabaseError(`Failed to update account: ${error.message}`, error.code);
    }

    return data;
  }
};

/**
 * Subscription Operations
 */
export const subscriptionOperations = {
  /**
   * Get user subscriptions
   */
  async findByUserId(userId: string): Promise<Subscription[]> {
    const { data, error } = await supabaseAdmin
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new DatabaseError(`Failed to fetch subscriptions: ${error.message}`, error.code);
    }

    return data || [];
  },

  /**
   * Create subscription
   */
  async create(subscriptionData: SubscriptionInsert): Promise<Subscription> {
    const { data, error } = await supabaseAdmin
      .from('subscriptions')
      .insert(subscriptionData)
      .select()
      .single();

    if (error) {
      throw new DatabaseError(`Failed to create subscription: ${error.message}`, error.code);
    }

    return data;
  },

  /**
   * Update subscription
   */
  async update(id: string, subscriptionData: SubscriptionUpdate): Promise<Subscription> {
    const { data, error } = await supabaseAdmin
      .from('subscriptions')
      .update(subscriptionData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new DatabaseError(`Failed to update subscription: ${error.message}`, error.code);
    }

    return data;
  },

  /**
   * Upsert subscription (create or update)
   */
  async upsert(subscriptionData: SubscriptionInsert): Promise<Subscription> {
    const { data, error } = await supabaseAdmin
      .from('subscriptions')
      .upsert(subscriptionData, { onConflict: 'id' })
      .select()
      .single();

    if (error) {
      throw new DatabaseError(`Failed to upsert subscription: ${error.message}`, error.code);
    }

    return data;
  }
};

/**
 * One-Time Purchase Operations
 */
export const oneTimePurchaseOperations = {
  /**
   * Get user purchases
   */
  async findByUserId(userId: string): Promise<OneTimePurchase[]> {
    const { data, error } = await supabaseAdmin
      .from('one_time_purchases')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new DatabaseError(`Failed to fetch purchases: ${error.message}`, error.code);
    }

    return data || [];
  },

  /**
   * Create purchase
   */
  async create(purchaseData: OneTimePurchaseInsert): Promise<OneTimePurchase> {
    const { data, error } = await supabaseAdmin
      .from('one_time_purchases')
      .insert(purchaseData)
      .select()
      .single();

    if (error) {
      throw new DatabaseError(`Failed to create purchase: ${error.message}`, error.code);
    }

    return data;
  }
};

/**
 * Verification Operations
 */
export const verificationOperations = {
  /**
   * Create verification
   */
  async create(verificationData: VerificationInsert): Promise<Verification> {
    const { data, error } = await supabaseAdmin
      .from('verifications')
      .insert(verificationData)
      .select()
      .single();

    if (error) {
      throw new DatabaseError(`Failed to create verification: ${error.message}`, error.code);
    }

    return data;
  },

  /**
   * Find verification by identifier and value
   */
  async findByIdentifierAndValue(identifier: string, value: string): Promise<Verification | null> {
    const { data, error } = await supabaseAdmin
      .from('verifications')
      .select('*')
      .eq('identifier', identifier)
      .eq('value', value)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new DatabaseError(`Failed to find verification: ${error.message}`, error.code);
    }

    return data;
  },

  /**
   * Delete verification
   */
  async delete(id: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('verifications')
      .delete()
      .eq('id', id);

    if (error) {
      throw new DatabaseError(`Failed to delete verification: ${error.message}`, error.code);
    }
  }
};

/**
 * Combined database operations object for easy import
 */
export const db = {
  user: userOperations,
  session: sessionOperations,
  account: accountOperations,
  subscription: subscriptionOperations,
  oneTimePurchase: oneTimePurchaseOperations,
  verification: verificationOperations,
};

export default db;
